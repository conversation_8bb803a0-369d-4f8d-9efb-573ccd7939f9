#version 330

in vec2 fragTexCoord;
in vec4 fragColor; // This will be the vertex color, not the note color

out vec4 finalColor;

uniform sampler2D textureSampler;
uniform vec4 tintColor; // The color of the note
uniform vec2 screenSize; // Screen width and height
uniform float noteBorder; // Border thickness

void main()
{
    vec4 texColor = texture(textureSampler, fragTexCoord);

    // Apply the tint color to the texture
    vec4 finalTintedColor = texColor * tintColor;

    // Recreate the border effect from the original Notes.fx shader
    // This assumes fragTexCoord is 0-1 for the note's local coordinates
    float borderH = round(noteBorder * screenSize.x) / screenSize.x;
    float borderV = round(noteBorder * screenSize.y) / screenSize.y / (screenSize.y / screenSize.x);

    // Outer dark border (similar to original shader's cl.xyz *= 0.2f; cl.xyz -= 0.05f;)
    vec4 outerColor = vec4(tintColor.rgb * 0.2f - 0.05f, tintColor.a);
    outerColor = clamp(outerColor, 0.0, 1.0);

    // Inner bright area (similar to original shader's cl.xyz += 0.1f; cr.xyz -= 0.3f;)
    vec4 innerColor = vec4(tintColor.rgb + 0.1f, tintColor.a);
    innerColor = clamp(innerColor, 0.0, 1.0);

    // Determine if current fragment is in the inner area or outer border
    // Using fragTexCoord for local note coordinates (0 to 1)
    bool isInner = fragTexCoord.x > borderH && fragTexCoord.x < (1.0 - borderH) &&
                   fragTexCoord.y > borderV && fragTexCoord.y < (1.0 - borderV);

    if (isInner)
    {
        // Apply inner color, blending with texture alpha
        finalColor = vec4(innerColor.rgb * texColor.a, finalTintedColor.a);
    }
    else
    {
        // Apply outer color, blending with texture alpha
        finalColor = vec4(outerColor.rgb * texColor.a, finalTintedColor.a);
    }

    // Ensure full opacity for the final note
    finalColor.a = 1.0;
}
