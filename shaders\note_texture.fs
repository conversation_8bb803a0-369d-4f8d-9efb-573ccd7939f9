#version 330

// Input from vertex shader
in vec2 fragTexCoord;
in vec4 fragColor;

// Output color
out vec4 finalColor;

// Uniforms
uniform sampler2D texture0;    // The note.png texture
uniform vec4 tintColor;        // The color of the note

void main()
{
    // Sample the texture - note.png already contains the border pattern
    // The texture is white where the note should be, with proper borders built-in
    vec4 texColor = texture(texture0, fragTexCoord);

    // Simple and efficient: just tint the white texture with the note color
    // The border effects are already baked into the texture
    finalColor = vec4(tintColor.rgb * texColor.rgb, tintColor.a * texColor.a);
}
