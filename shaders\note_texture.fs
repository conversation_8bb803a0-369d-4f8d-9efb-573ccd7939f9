#version 330

// Input from vertex shader
in vec2 fragTexCoord;
in vec4 fragColor;

// Output color
out vec4 finalColor;

// Uniforms
uniform sampler2D texture0;    // The note.png texture
uniform vec4 tintColor;        // The color of the note
uniform vec2 screenSize;       // Screen width and height
uniform float noteBorder;      // Border thickness (0.00091)

void main()
{
    // Sample the texture (note.png is white, so we can tint it)
    vec4 texColor = texture(texture0, fragTexCoord);
    
    // Calculate border dimensions exactly like the original Notes.fx shader
    float borderH = round(noteBorder * screenSize.x) / screenSize.x;
    float borderV = round(noteBorder * screenSize.y) / screenSize.y / (screenSize.y / screenSize.x);
    
    // Determine if current fragment is in the border area or inner area
    // fragTexCoord ranges from 0 to 1 for the note's local coordinates
    bool isInBorder = fragTexCoord.x < borderH || fragTexCoord.x > (1.0 - borderH) ||
                      fragTexCoord.y < borderV || fragTexCoord.y > (1.0 - borderV);
    
    vec4 resultColor;
    
    if (isInBorder)
    {
        // Outer dark border (matching original shader's cl.xyz *= 0.2f; cl.xyz -= 0.05f;)
        vec3 borderColor = tintColor.rgb * 0.2 - 0.05;
        borderColor = clamp(borderColor, 0.0, 1.0);
        resultColor = vec4(borderColor, tintColor.a);
    }
    else
    {
        // Inner bright area (matching original shader's cl.xyz += 0.1f;)
        vec3 innerColor = tintColor.rgb + 0.1;
        innerColor = clamp(innerColor, 0.0, 1.0);
        resultColor = vec4(innerColor, tintColor.a);
    }
    
    // Apply texture alpha and final tinting
    finalColor = vec4(resultColor.rgb * texColor.a, resultColor.a * texColor.a);
}
